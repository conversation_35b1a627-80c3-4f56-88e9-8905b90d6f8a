# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

This is a mobile-first online store application built with **Python** and **Flet** framework, implementing clean architecture with offline-first functionality. The app supports two SQLite databases (`almishop.db` and `metallobaza.db`) that can be switched dynamically, each with their own branding and product catalogs.

### Architecture

The project follows a **3-layer clean architecture**:
1. **Presentation Layer** (`views/`) - Flet UI components and pages
2. **Logic Layer** (`controllers/`) - Application logic and state management  
3. **Data Layer** (`data/`) - SQLite operations, repositories, and API stubs

## Essential Commands

### Development Setup
```bash
# Install dependencies (prefer uv over pip)
uv sync

# Run the application (NEVER use "python app.py" or similar)
# This is a Flet app managed by uv, so use:
uv run flet run

# Run a specific file for testing
uv run flet run path/to/file.py
```

### Database Operations
```bash
# Check database files exist
ls data_local/dbs/

# View database schema (requires sqlite3)
sqlite3 data_local/dbs/almishop.db ".schema"
sqlite3 data_local/dbs/metallobaza.db ".schema"
```

### Project Structure Validation
```bash
# Verify all core directories exist
ls -la controllers/ data/ views/ core/

# Check if assets directory is set up
ls -la assets/branding/
```

## Key Architecture Components

### Database Schema
- **catalogs**: `(id, name)` - Product categories
- **products**: Full product info with `parent_id` linking to catalog entries
- **Filtering**: Uses `parent_id` relationships, NOT text search

### Controllers Pattern
- `AppController`: Global state and database switching
- `AuthController`: Login/authentication (stubbed in development)
- `CatalogController`: Product filtering and catalog management
- `CartController`: Shopping cart operations

### Data Layer Components
- `DatabaseManager`: SQLite connections and database switching
- `Repository`: Unified data access interface
- `ApiClient`: Authentication and sync stubs (development mode always returns True)

## Development Workflow

### Working with Multiple Databases
The app supports switching between `almishop.db` and `metallobaza.db`:
- Each database has independent product catalogs
- Switching updates branding (logos: `almishop.png`/`metallobaza.png`)
- Database selection persists during app session

### SafeArea Implementation
All UI components must respect Android SafeArea constraints:
- Use Flet's SafeArea control to avoid device notches/cutouts
- Test layouts on different screen sizes and orientations

### Adding New Features
1. **Models**: Add to `data/models/` with proper dataclass structure
2. **Repository**: Extend `data/repository.py` with new data access methods
3. **Controller**: Create business logic in appropriate controller
4. **View**: Implement UI in `views/` following existing patterns

## Configuration

### Environment Modes
- `DEVELOPMENT_MODE = True` (in `core/config.py`): All logins succeed, API calls stubbed
- Production mode: Requires actual API endpoints for authentication and sync

### Database Paths
Configured in `core/config.py`:
- `almishop`: `data_local/dbs/almishop.db`  
- `metallobaza`: `data_local/dbs/metallobaza.db`

## Testing Strategy

Currently no test suite exists. When adding tests:
- Focus on controller logic and data layer operations
- Test database switching functionality
- Validate cart calculations and state management
- Test offline functionality (no internet required)

## Current Development Status

Based on TaskList-OnlineStoreApp.md, the project is partially implemented:
- ✅ Basic project structure and configuration
- ✅ Core data layer (DatabaseManager, Repository, models)
- ✅ Basic controllers (AppController, CartController, AuthController)
- ✅ Initial login page implementation
- 🔄 In Progress: Full UI views and navigation system
- ❌ TODO: Product detail pages, cart UI, database switching UI
- ❌ TODO: Branding system and SafeArea implementation

## Important Files

### Core Configuration
- `main.py`: Application entry point and Flet initialization
- `core/config.py`: Database paths, development mode settings
- `pyproject.toml`: Python dependencies (uses Flet 0.28.3)

### Data Models
- `data/models/product.py`: Product dataclass structure
- `data/models/catalog.py`: Catalog dataclass structure

### Critical Implementation Notes
- All database operations go through Repository pattern
- Authentication is stubbed in development (always succeeds)
- Cart state is session-only (not persisted between app restarts)
- UI must handle database switching without losing current page state