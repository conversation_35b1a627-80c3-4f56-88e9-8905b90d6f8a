from data.db_manager import DatabaseManager
from data.models.product import Product
from data.models.catalog import Catalog
class Repository:
    def __init__(self): self.db = DatabaseManager()
    def get_catalogs(self):
        rows = self.db.query("SELECT id, name FROM catalogs ORDER BY id")
        return [Catalog(r["id"], r["name"]) for r in rows]
    def get_products_by_catalog_ids(self, ids):
        if not ids:
            rows = self.db.query("SELECT * FROM products")
        else:
            placeholders = ",".join("?" * len(ids))
            rows = self.db.query(f"SELECT * FROM products WHERE parent_id IN ({placeholders})", ids)
        return [Product(**r) for r in rows]
    def switch_database(self, db_key): self.db.switch_database(db_key)
