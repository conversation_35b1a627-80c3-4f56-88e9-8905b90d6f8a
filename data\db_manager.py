import sqlite3
from core.config import DB_PATHS, DEFAULT_DB
class DatabaseManager:
    def __init__(self):
        self.active_db = DEFAULT_DB
        self.conn = None
        self.connect()
    def connect(self):
        self.conn = sqlite3.connect(DB_PATHS[self.active_db])
        self.conn.row_factory = sqlite3.Row
    def switch_database(self, db_key):
        self.active_db = db_key
        self.connect()
    def query(self, sql, params=None):
        cur = self.conn.cursor()
        cur.execute(sql, params or [])
        return cur.fetchall()
