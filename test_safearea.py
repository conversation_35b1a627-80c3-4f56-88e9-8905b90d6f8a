import flet as ft
from views.components.safe_area import create_safe_page


def main(page: ft.Page):
    """
    Test script to demonstrate SafeArea functionality with different layouts.
    This helps verify that SafeArea works correctly on different screen sizes.
    """
    page.title = "SafeArea Test"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0
    
    # Test content with various layouts
    test_content = ft.Column(
        [
            ft.Container(
                content=ft.Text(
                    "SafeArea Test", 
                    size=28, 
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER
                ),
                bgcolor="#E3F2FD",
                padding=ft.padding.all(20),
                border_radius=10,
                margin=ft.margin.only(bottom=20)
            ),
            ft.Container(
                content=ft.Column([
                    ft.Text("Content should not overlap with:", size=16),
                    ft.Text("• Status bar (top)", size=14),
                    ft.Text("• Navigation bar (bottom)", size=14),
                    ft.Text("• Notches or camera cutouts", size=14),
                    ft.Text("• Screen edges", size=14),
                ]),
                bgcolor="#E8F5E8",
                padding=ft.padding.all(15),
                border_radius=10,
                margin=ft.margin.only(bottom=20)
            ),
            ft.Container(
                content=ft.Row([
                    ft.ElevatedButton("Button 1", expand=True),
                    ft.ElevatedButton("Button 2", expand=True),
                    ft.ElevatedButton("Button 3", expand=True),
                ]),
                bgcolor="#FFF3E0",
                padding=ft.padding.all(15),
                border_radius=10,
            ),
        ],
        spacing=0,
        expand=True,
    )
    
    # Wrap test content with SafeArea
    safe_content = create_safe_page(test_content)
    page.add(safe_content)


if __name__ == "__main__":
    ft.app(target=main)