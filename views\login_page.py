import flet as ft
from controllers.auth_controller import AuthController


class LoginPage(ft.Container):
    def __init__(self, auth_controller: AuthController, on_login_success):
        self.auth_controller = auth_controller
        self.on_login_success = on_login_success

        # Create form controls
        self.username = ft.TextField(label="Username", width=300)
        self.password = ft.TextField(label="Password", password=True, width=300)
        self.login_btn = ft.ElevatedButton("Login", on_click=self.login, width=300)

        # Create the login form layout
        login_form = ft.Column(
            [
                ft.Text("Login to Almishop", size=24, weight=ft.FontWeight.BOLD),
                ft.Container(height=20),  # Spacer
                self.username,
                self.password,
                ft.Container(height=10),  # Spacer
                self.login_btn,
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        )

        # Initialize the container with the form
        super().__init__(
            content=login_form,
            alignment=ft.alignment.center,
            expand=True,
        )

    def login(self, e):
        if self.auth_controller.login(self.username.value, self.password.value):
            self.on_login_success()
        else:
            # Show login failed message
            snack_bar = ft.SnackBar(
                ft.Text("Login failed"),
                behavior=ft.SnackBarBehavior.FLOATING,
                dismiss_direction=ft.DismissDirection.HORIZONTAL,
                duration=3000,
                margin=ft.margin.only(left=20, right=20, bottom=20),
                bgcolor=ft.Colors.RED_100,
                show_close_icon=True,
                close_icon_color=ft.Colors.WHITE,
            )
            snack_bar.open = True

            e.page.overlay.append(snack_bar)
            e.page.update()
