import flet as ft


class LoginPage(ft.View):
    def __init__(self, auth_controller, on_login_success):
        super().__init__()
        self.auth_controller, self.on_login_success = auth_controller, on_login_success
        self.username = ft.TextField(label="Username")
        self.password = ft.TextField(label="Password", password=True)
        self.login_btn = ft.ElevatedButton("Login", on_click=self.login)
        self.controls = [ft.Column([self.username, self.password, self.login_btn])]

    def login(self, e):
        if self.auth_controller.login(self.username.value, self.password.value):
            self.on_login_success()
        else:
            self.page.snack_bar = ft.SnackBar(ft.Text("Login failed"))
            self.page.snack_bar.open = True
            self.page.update()
