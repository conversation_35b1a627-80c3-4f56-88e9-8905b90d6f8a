# Online Store App - Actionable Task List

Based on SPEC-OnlineStoreApp.md, this document breaks down the development into 5 phases with actionable tasks.

## Phase 1: Project Setup and SafeArea Layout Validation

### Tasks:
1. [*] **Environment Setup**
   - [*] Install Python dependencies including Flet using `uv add flet`
   - [*] Create the project folder structure as defined in specification
   - [*] Set up version control (git) for the project

2. [*] **Core Configuration**
   - [*] Create `core/config.py` with database paths and brand configuration constants
   - [*] Create `core/utils.py` with helper functions for file operations and SafeArea utilities
   - [*] Define environment variables for development vs production modes

3. [*] **Asset Preparation** 
   - [*] Create `assets/branding/` directory
   - [*] Add placeholder logo files: `almishop.png` and `metallobaza.png`
   - [*] Set up database directory structure: `data_local/dbs/`

4. [ ] **Basic Flet App Structure**
   - [*] Create `main.py` with basic Flet app initialization
   - [ ] Implement SafeArea wrapper component to handle device cutouts and notches
   - [ ] Test SafeArea layout on different screen sizes/orientations

5. [*] **Database Files Setup**
   - [*] Create empty SQLite database files: `almishop.db` and `metallobaza.db`
   - [*] Verify database file locations match specification paths

## Phase 2: Build UI Views and Navigation

### Tasks:
1. [ ] **Login Page Implementation**
   - [ ] Create `views/login_page.py` with username/password input fields
   - [ ] Add login button and basic form validation
   - [ ] Implement navigation to catalog page on successful authentication
   - [ ] Apply SafeArea constraints to page

2. [ ] **Catalog Page Layout**
   - [ ] Create `views/catalog_page.py` with main catalog structure
   - [ ] Implement filter strip placeholder (horizontal scrollable buttons)
   - [ ] Create product grid layout with responsive card components
   - [ ] Add cart icon button in app bar
   - [ ] Apply SafeArea constraints to page elements

3. [ ] **Product Detail Page**
   - [ ] Create `views/product_page.py` with image carousel placeholder
   - [ ] Add product details section (name, price, description fields)
   - [ ] Implement quantity selector and "Add to Cart" button
   - [ ] Add fullscreen image view capability
   - [ ] Apply SafeArea constraints to page

4. [ ] **Cart Page Structure**
   - [ ] Create `views/cart_page.py` with cart items list view
   - [ ] Add order summary section (subtotal, total display)
   - [ ] Implement "Place Order" button with confirmation popup
   - [ ] Create empty cart state display
   - [ ] Apply SafeArea constraints to page

5. [ ] **Menu Panel (Slide-out)**
   - [ ] Create `views/menu_panel.py` with database selection dropdown
   - [ ] Add "Refresh Database" button
   - [ ] Implement slide-out animation and menu toggle functionality

6. [ ] **Navigation System**
   - [ ] Implement page routing between all views
   - [ ] Add back button functionality for navigation stack
   - [ ] Test navigation flow: Login → Catalog → Product → Cart

## Phase 3: Database Schema Setup and Repository Layer

### Tasks:
1. [ ] **Database Schema Implementation**
   - [ ] Create SQL scripts for `catalogs` table with id and name fields
   - [ ] Create SQL scripts for `products` table with all specified columns
   - [ ] Implement database migration/initialization logic

2. [ ] **Data Models**
   - [ ] Create `data/models/catalog.py` with Catalog dataclass/model
   - [ ] Create `data/models/product.py` with Product dataclass/model
   - [ ] Add validation and serialization methods to models

3. [ ] **Database Manager**
   - [ ] Create `data/db_manager.py` with SQLite connection management
   - [ ] Implement database switching functionality between almishop.db and metallobaza.db
   - [ ] Add database initialization and schema creation methods
   - [ ] Implement connection pooling and proper resource cleanup

4. [ ] **Repository Layer**
   - [ ] Create `data/repository.py` with unified data access interface
   - [ ] Implement catalog retrieval methods (get_all_catalogs)
   - [ ] Implement product retrieval methods (get_products_by_catalog, get_product_by_id)
   - [ ] Add filtering logic based on parent_id relationships

5. [ ] **API Client Stub**
   - [ ] Create `data/api_client.py` with stubbed authentication methods
   - [ ] Implement mock login validation (returns success for development)
   - [ ] Add placeholder methods for future database synchronization
   - [ ] Create interface for production API integration

6. [ ] **Test Data Population**
   - [ ] Create sample catalog entries in both databases
   - [ ] Add sample product entries with proper parent_id relationships
   - [ ] Verify data integrity and filtering relationships work correctly

## Phase 4: Implement Controllers and Basic Logic Flows

### Tasks:
1. [ ] **Authentication Controller**
   - [ ] Create `controllers/auth_controller.py` with login logic
   - [ ] Integrate with ApiClient for authentication validation
   - [ ] Implement session management and login state tracking
   - [ ] Handle authentication success/failure scenarios

2. [ ] **Catalog Controller**
   - [ ] Create `controllers/catalog_controller.py` for filtering and product management
   - [ ] Implement catalog filter toggle logic (multi-select functionality)
   - [ ] Add product grid data binding and refresh methods
   - [ ] Handle product selection and navigation to detail view

3. [ ] **Cart Controller**
   - [ ] Create `controllers/cart_controller.py` for cart operations
   - [ ] Implement add/remove product functionality
   - [ ] Add quantity management and price calculation logic
   - [ ] Handle cart persistence during app session

4. [ ] **App Controller (Global State)**
   - [ ] Create `controllers/app_controller.py` for database switching
   - [ ] Implement brand theme switching logic (logos and colors)
   - [ ] Add global state management for active database
   - [ ] Handle database refresh trigger functionality

5. [ ] **Event Binding Integration**
   - [ ] Connect UI components to controller methods
   - [ ] Implement event handlers for user interactions (clicks, selections)
   - [ ] Add form submission and validation event handling
   - [ ] Test all user interaction flows work correctly

6. [ ] **Cart Functionality Testing**
   - [ ] Verify add to cart from catalog and product detail pages
   - [ ] Test quantity updates and price calculations
   - [ ] Confirm cart data persists during navigation
   - [ ] Test order placement confirmation popup

## Phase 5: Integration, Branding, and Polish

### Tasks:
1. [ ] **Database Switching Integration**
   - [ ] Connect menu dropdown to database switching logic
   - [ ] Implement dynamic logo updates (almishop.png ↔ metallobaza.png)
   - [ ] Add theme color switching based on active database
   - [ ] Test seamless switching between databases preserves app state

2. [ ] **Branding and Visual Polish**
   - [ ] Apply almishop.by inspired color schemes and fonts
   - [ ] Implement proper logo display scaling and positioning
   - [ ] Add consistent styling across all pages and components
   - [ ] Ensure visual consistency matches brand identity

3. [ ] **Responsive Design Refinement**
   - [ ] Test layout scaling across different Android screen sizes
   - [ ] Refine SafeArea implementations for various device cutouts
   - [ ] Optimize touch targets and spacing for mobile interaction
   - [ ] Verify horizontal/vertical orientation handling

4. [ ] **Animation Implementation (Should Have)**
   - [ ] Add page transition animations between views
   - [ ] Implement filter toggle animations in catalog strip
   - [ ] Add toast notification fade-in/out effects
   - [ ] Create smooth cart icon badge updates

5. [ ] **Local Cart Persistence (Should Have)**
   - [ ] Implement cart data persistence between app sessions
   - [ ] Add cart recovery on app restart
   - [ ] Handle cart data cleanup and expiration logic

6. [ ] **Final Testing and Validation**
   - [ ] Perform end-to-end testing of all user flows
   - [ ] Test offline functionality without internet connection
   - [ ] Verify all Must Have requirements are implemented
   - [ ] Test database switching preserves filtered state appropriately

7. [ ] **Performance Optimization**
   - [ ] Optimize image loading and caching for product images
   - [ ] Implement lazy loading for large product catalogs
   - [ ] Profile app performance and memory usage
   - [ ] Optimize database query performance for filtering

8. [ ] **Code Quality and Documentation**
   - [ ] Add comprehensive docstrings to all modules and functions
   - [ ] Implement error handling and user-friendly error messages
   - [ ] Create unit tests for controller logic and data layer
   - [ ] Generate final project documentation

---

## Summary
- **Phase 1:** 5 tasks - Foundation and setup
- **Phase 2:** 6 tasks - UI views and navigation
- **Phase 3:** 6 tasks - Data layer implementation  
- **Phase 4:** 6 tasks - Business logic and controllers
- **Phase 5:** 8 tasks - Integration and polish

**Total: 31 actionable tasks across 5 phases**

Each task is designed to be completable within a development session and builds upon previous tasks to ensure steady progress toward the complete application.