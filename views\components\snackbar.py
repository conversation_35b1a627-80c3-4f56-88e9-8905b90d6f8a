import flet as ft
from core.types import SnackType

def create_snackbar(message, snack_type=SnackType.ERROR):
    """Create a snackbar with the specified message and type."""
    return ft.SnackBar(
        content=ft.Text(message, color=ft.Colors.WHITE),
        bgcolor=snack_type.value,
        behavior=ft.SnackBarBehavior.FLOATING,
        dismiss_direction=ft.DismissDirection.HORIZONTAL,
        duration=3000,
        margin=ft.margin.only(left=20, right=20, bottom=20),
        show_close_icon=True,
        close_icon_color=ft.Colors.WHITE,
    )
