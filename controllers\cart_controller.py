class CartController:
    def __init__(self):
        self.cart = {}

    def add_to_cart(self, product, qty=1):
        if product.id in self.cart:
            self.cart[product.id]["qty"] += qty
        else:
            self.cart[product.id] = {"product": product, "qty": qty}

    def remove_from_cart(self, product_id):
        self.cart.pop(product_id, None)

    def get_cart_items(self):
        return self.cart.values()

    def calculate_totals(self):
        return sum(
            float(item["product"].price) * item["qty"] for item in self.cart.values()
        )
