import flet as ft
from controllers.auth_controller import AuthController
from views.login_page import LoginPage

def main(page: ft.Page):
    auth_controller = AuthController()
    def open_catalog():
        page.clean()
        page.add(ft.Text("Catalog screen placeholder"))
    login_view = LoginPage(auth_controller, open_catalog)
    page.add(login_view)

if __name__ == "__main__":
    ft.app(target=main)
