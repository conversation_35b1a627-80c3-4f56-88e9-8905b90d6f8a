import flet as ft
from controllers.auth_controller import AuthController
from views.login_page import LoginPage
from views.components.safe_area import create_safe_page


def main(page: ft.Page):
    # Configure page settings for mobile app
    page.title = "Almishop App"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 0  # Let SafeArea handle padding
    
    auth_controller = AuthController()

    def open_catalog():
        page.clean()
        catalog_placeholder = ft.Text(
            "Catalog screen placeholder", 
            size=24,
            text_align=ft.TextAlign.CENTER
        )
        safe_catalog = create_safe_page(catalog_placeholder)
        page.add(safe_catalog)

    login_view = LoginPage(auth_controller, open_catalog)
    safe_login = create_safe_page(login_view)
    page.add(safe_login)


if __name__ == "__main__":
    ft.app(target=main)
