import flet as ft


def create_safe_area_wrapper(content):
    """
    Creates a SafeArea wrapper to handle device cutouts, notches, and status bars.
    Ensures content doesn't overlap with system UI elements on mobile devices.

    Args:
        content: The Flet control to wrap with SafeArea

    Returns:
        SafeArea control containing the wrapped content
    """
    # Use Flet's built-in SafeArea control to handle device cutouts
    return ft.SafeArea(
        content=ft.Container(
            content=content,
            padding=ft.padding.all(8),  # Additional padding for better visual spacing
            expand=True,
        ),
        left=True,
        top=True,
        right=True,
        bottom=True,
    )


def create_safe_page(content):
    """
    Convenience function to wrap any content with SafeArea constraints.

    Args:
        content: The Flet control to wrap with SafeArea

    Returns:
        SafeArea control containing the wrapped content
    """
    return create_safe_area_wrapper(content)
