# SPEC-4 - Online Store App (Clean Architecture)

## Background

This document defines a full architecture and functional specification for a mobile one-page online store application built using **Python** and **Flet**. The primary goals are offline functionality, modular design, and adaptability for future API integration.

The system uses two local SQLite databases (`almishop.db` and `metallobaza.db`) that represent distinct product collections and branding identities. The user can switch between them dynamically via an in-app menu. Each database has a `catalogs` table defining product categories and a `products` table containing items related by `parent_id`. Filtering is based exclusively on these catalog relationships.

The architecture follows a **clean separation of concerns** with three core layers:

1. **Presentation Layer (Views)** – UI components, pages, and layouts built in Flet.
2. **Logic Layer (Controllers)** – Application logic and state management.
3. **Data Layer (Repositories and Managers)** – Handles all SQLite operations, data retrieval, and API stubs.

By decoupling these layers, developers can maintain and extend each independently, ensuring modularity, testability, and scalability. The design allows for smooth offline operation and easy transition to an online-enabled version when APIs become available.

The visual and brand identity is derived from **almishop.by**, including fonts, colors, and layout inspiration. When the user switches from one database to another (e.g., to `metallobaza.db`), the logo and theme colors change accordingly (`almishop.png` or `metallobaza.png`).

All UI components respect Android SafeArea constraints to avoid overlapping with notches, camera cutouts, and status bars. The app’s layout scales responsively across all Android devices, regardless of screen size or resolution.

## Requirements

### Must Have (M)

1. **Authentication & Startup**
   - A login screen with username and password fields is shown at app launch.
   - During development, all logins succeed (stubbed logic).
   - In production, authentication is validated via an API call to a remote endpoint (`POST username/password`).
   - On successful login, transition to the catalog page.

2. **Data Sources & Switching**
   - The app stores two SQLite databases locally in `/data_local/dbs`.
   - Users can switch databases using a dropdown in the slide-out menu.
   - The active logo and theme update dynamically upon switch.
   - “Refresh Database” button triggers synchronization logic (mocked in development, API-enabled later).

3. **Data Model & Filtering**
   - Databases contain `catalogs` and `products` tables.
     - `catalogs`: `(id INTEGER PRIMARY KEY, name TEXT NOT NULL, ...)` the only important fields here are name and id, the rest are for other projects
     - `products`: includes standard fields (`id, name, price, image, images, parent_id, vendor, description, ...`)
   - Filtering is done by `parent_id`, corresponding to selected catalogs.
   - The filter strip displays catalog names; users can multi-select catalogs.

4. **Catalog & Product Grid**
   - The main catalog view shows:
     - Filter strip with toggleable catalog names.
     - Grid of product cards with image, name, price, and “Add to Cart” button.
   - Tapping a filter button updates the product grid dynamically.

5. **Product Detail View**
   - Tapping a product card opens a detailed product view with:
     - Image carousel and fullscreen image view.
     - Product details: name, price, attributes, vendor, and description.
     - “Add to Cart” button with quantity input.

6. **Cart & Checkout**
   - A shopping cart icon opens the cart view.
   - Displays product list (image, name, qty, unit price, subtotal, total).
   - “Place Order” shows popup message confirming order creation.
   - Cart data is stored locally during app session.

7. **Branding & Logo**
   - The app displays the current brand logo depending on active DB:
     - `almishop.png` for Almishop
     - `metallobaza.png` for Metallobaza
   - Visual colors and fonts align with corresponding brand identities.

8. **SafeArea & Responsiveness**
   - Layouts use Flet’s SafeArea control to avoid overlap with device cutouts.
   - UI elements scale and reposition dynamically to maintain usability.

### Should Have (S)

- Animated transitions (page change, toast fade-outs, filter toggle).
- Local cart persistence between sessions.

### Could Have (C)

- Future backend synchronization for orders.
- Text-based search within catalog view.

### Won’t Have (W)

- Online-only dependencies or mandatory internet connection.
- User registration and advanced profile management.

## Method

### Architectural Overview

The app implements a **three-layer clean architecture**:

1. **Presentation Layer (Views)** – Handles all visual rendering and user interaction through Flet.
2. **Logic Layer (Controllers)** – Contains logic for authentication, catalog filtering, cart management, and database switching.
3. **Data Layer (Repositories, Database Managers, and API Clients)** – Interfaces with SQLite for storage and optionally a remote API for sync or authentication.

### Folder Structure

```
/app
  main.py
  core/
    config.py
    utils.py
  views/
    login_page.py
    catalog_page.py
    product_page.py
    cart_page.py
    menu_panel.py
  controllers/
    app_controller.py
    auth_controller.py
    catalog_controller.py
    cart_controller.py
  data/
    db_manager.py
    api_client.py
    repository.py
    models/
      product.py
      catalog.py
  assets/
    branding/
      almishop.png
      metallobaza.png
  data_local/
    dbs/
      almishop.db
      metallobaza.db
```

### Database Schemas

**Catalogs Table**
```sql
CREATE TABLE catalogs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL
);
```

**Products Table (common structure across both DBs)**
```sql
CREATE TABLE products (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  articul TEXT,
  price TEXT,
  created_at TEXT,
  updated_at TEXT,
  slug TEXT,
  price_old TEXT,
  image TEXT,
  images TEXT,
  parent_id INTEGER,
  in_stock INTEGER DEFAULT 0,
  characts TEXT,
  vendor TEXT,
  description TEXT,
  pos INTEGER,
  length TEXT,
  country TEXT,
  weight TEXT,
  content TEXT,
  is_active INTEGER DEFAULT 1,
  is_guarded INTEGER DEFAULT 0
);
```

### Data Layer Components

- **DatabaseManager:** Handles connections, queries, and DB switching.
- **Repository:** Provides unified access to product and catalog data.
- **ApiClient:** Handles login and DB refresh (stubbed during offline dev).

### Controllers

- **AuthController:** Manages login via `ApiClient`.
- **CatalogController:** Manages catalog filtering and product retrieval.
- **CartController:** Handles cart logic (add, remove, total calculation).
- **AppController:** Oversees database switching and global state.

### Views

- **LoginPage:** Handles user login UI and navigation to catalog page.
- **CatalogPage:** Displays filter strip and product grid.
- **ProductPage:** Displays detailed product info.
- **CartPage:** Displays cart contents and checkout summary.
- **MenuPanel:** Allows DB selection and refresh.

## Implementation

Implementation proceeds in the following order:
1. Setup Flet environment and folder structure.
2. Implement models (`Product`, `Catalog`).
3. Implement DatabaseManager, Repository, and ApiClient.
4. Implement controllers for app logic.
5. Implement Flet views (LoginPage → CatalogPage → ProductPage → CartPage).
6. Add event bindings between UI and controllers.
7. Implement DB switch and branding updates.

## Milestones

1. **Phase 1:** Project setup and SafeArea layout validation.
2. **Phase 2:** Build UI views and navigation.
3. **Phase 3:** Database schema setup and repository layer.
4. **Phase 4:** Implement controllers and basic logic flows.
5. **Phase 5:** Integration, branding, and polish.

## Gathering Results

System performance and correctness are validated by:
- Verifying that filtering, DB switching, and branding behave as expected.
- Confirming that all features work offline.
- Testing across devices with different screen dimensions.